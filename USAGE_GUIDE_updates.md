## 5. Interactive Mode and Human-in-the-Loop Features

To enhance control, reliability, and the accuracy of generated content, interactive human-in-the-loop (HITL) steps have been integrated into both the `research_agent` and `paper_agent` workflows. These steps allow users to review, edit, and guide the generation process at critical junctures.

### 5.1 Introduction to Interactive Mode

Interactive mode provides opportunities for users to inject their expertise, correct misunderstandings by the LLM, or steer the project in a preferred direction. This is particularly useful for complex tasks where the LLM's initial output might require refinement or when specific custom inputs are needed.

### 5.2 Using Interactive Features

The interactive features primarily use two types of prompts:

1.  **Choosing from a list (`present_choices_to_user`):**
    *   You will be presented with a numbered list of options.
    *   To select an option, type the corresponding number and press Enter.
    *   In some cases, you might be offered an option to provide a custom input (e.g., by typing 'C').

2.  **Reviewing and Editing Text (`present_text_to_user_for_review`):**
    *   When a piece of text (like a generated idea, a plan, or a paper section draft) is ready for your review, it will be saved to a temporary file.
    *   The path to this file will be displayed in the console (e.g., `temp_review_files/some_content_for_review.txt`). This path is typically relative to the script's current working directory (e.g., project root for `research_agent` scripts, or the `paper_output_dir` for `paper_agent` scripts).
    *   You will be prompted to either:
        *   **[A]pprove** the content as is.
        *   **[E]dit** the content. If you choose to edit:
            1.  Open the specified temporary file in your preferred text editor.
            2.  Make your changes and save the file.
            3.  Return to the console and type 'D' (or follow the specific prompt, usually just pressing Enter after being prompted that you can now edit) to confirm you are done editing. The script will then read the modified content from the file.
    *   These temporary review files are generally deleted automatically after the interaction is complete to keep the workspace clean.

### 5.3 Interaction Points in `research_agent`

#### 1. Idea Selection (when running `run_infer_idea.py`)

*   **Timing:** This interaction occurs after the `IdeaAgent` has generated multiple initial ideas and then selected and refined one primary idea.
*   **Process:**
    *   The user is presented with a list of choices:
        1.  Review/Approve the LLM's selected and refined idea.
        2.  Review/Approve any of the alternative original ideas generated by the LLM.
        3.  Option to provide a file path to a custom idea (if you've prepared one).
        4.  Option to paste or type a custom idea directly into the console.
    *   If you choose to review an existing idea (LLM-refined or one of the originals), its content will be saved to a temporary file for your inspection and potential editing via the `present_text_to_user_for_review` mechanism.
*   **Impact:** The idea you select, approve, or provide (potentially after your edits) becomes the core research concept. This `survey_res` (survey result) is then used for subsequent steps, including the code survey, detailed planning, and implementation by the `MLAgent`.

#### 2. Plan Validation (when running `run_infer_plan.py` or `run_infer_idea.py`)

*   **Timing:** This step occurs after the `CodingPlanAgent` has generated the detailed implementation plan for the selected research idea.
*   **Process:**
    *   The generated coding plan (often in Markdown format) is saved to a temporary file (e.g., `temp_review_files/coding_plan_for_review.md`).
    *   You are prompted to review this file using the `present_text_to_user_for_review` mechanism. You can approve the plan as is or edit the file to make corrections or add details.
*   **Impact:** The script will only proceed with the coding and implementation phases once you have approved (or edited and then approved) the plan. If the review process is cancelled or an error occurs (e.g., the edited file cannot be read), the script will halt, preventing further execution with a potentially flawed plan.

### 5.4 Interaction Points in `paper_agent`

#### 1. Paper Section Draft Review (during `paper_agent/writing.py` execution)

*   **Timing:** This review occurs within each individual paper section composing module (e.g., `MethodologyComposer`, `IntroductionComposer`, `RelatedWorkComposer`, etc.). It happens after the composer has generated an initial draft of that section by fusing various inputs (like LLM-generated subsections, agent JSONs, or other paper sections) but *before* any final LLM-based polishing or checklist is applied to that section.
*   **Process:**
    *   The drafted LaTeX content for the current section (e.g., methodology, introduction) is saved to a temporary file. This file is typically located within a `temp_review_files` subdirectory inside your specified `paper_output_dir` (e.g., `<paper_output_dir>/temp_review_files/your_instance_id_methodology_draft_for_review.tex`).
    *   You are prompted to review this `.tex` file using the `present_text_to_user_for_review` mechanism. You can approve the draft or edit it directly.
*   **Impact:** The content you approve or edit is then passed to the final LLM checklist/polishing step for that section (if applicable) and subsequently saved as the final output for that section (e.g., `methodology.tex`). If you cancel the review for a particular section, the composition of that specific section halts (often raising a `SystemExit`), though other sections might still be processed if the script is designed to handle this (currently, it will likely halt the entire `writing.py` process).

---

**Updates to Existing Sections (Conceptual - to be integrated into the main `USAGE_GUIDE.md`)**

**(To be added to Section 1: Overview of the Automated Paper Generation Workflow - if it exists, or as a general note)**

The pipeline now incorporates several human-in-the-loop (HITL) interaction points. These steps allow for user review and modification of key artifacts like research ideas, coding plans, and paper section drafts, offering greater control and potentially improving the quality and relevance of the final outputs. Details of these interactive features are covered in Section 5.

**(To be added to Section 3: Strategies for Improving Stability and Success Rate - under Debugging Tips)**

*   **Interactive Step Refinement:** If an interactive step (e.g., plan validation, section draft review) is approved but leads to poor LLM outputs in subsequent automated steps, it might indicate that the LLM misunderstood the (potentially edited) content. In such cases, re-running the pipeline and being more explicit, clear, or detailed in the content you provide or edit during the interactive review can be beneficial. For instance, if a reviewed plan is still misinterpreted by the `MLAgent`, ensure the edited plan is unambiguous.

---
