# Usage Guide: Automated Research Paper Generation

This guide provides an overview of the automated paper generation workflow, instructions on how to run the full pipeline, strategies for improving stability, and a summary of the key directory structures.

## 1. Overview of the Automated Paper Generation Workflow

The automated paper generation process involves two main stages:

1.  **`research_agent` Stage:**
    *   This stage uses Large Language Models (LLMs) to generate research artifacts. It can be initiated in two ways:
        *   `run_infer_plan.py`: For user-provided ideas/plans, typically defined in a benchmark JSON file.
        *   `run_infer_idea.py`: For LLM-generated ideas based on a research category.
    *   The `research_agent` produces Python code for models, experiment plans, evaluation results, and detailed agent interaction logs in JSON format. These artifacts serve as the primary input for the next stage.

2.  **`paper_agent` Stage:**
    *   This stage, orchestrated by `paper_agent/writing.py`, consumes the artifacts generated by the `research_agent`.
    *   It employs a series of specialized "composer" modules (e.g., for methodology, introduction, experiments) to draft each section of the research paper in LaTeX format.

Significant effort has been made to address and streamline the pathing configurations between these two stages, allowing for more robust and configurable execution.

## 2. How to Run the Full Pipeline

Follow these steps to run the end-to-end paper generation pipeline:

### Step 1: Run `research_agent` script

First, execute either `run_infer_plan.py` or `run_infer_idea.py` from the `research_agent` directory.

*   **For user-provided ideas (using a benchmark file):**
    ```bash
    cd research_agent
    python run_infer_plan.py \
        --instance_path ../benchmark/final/your_category/your_instance_id.json \
        --model "gpt-4o-2024-08-06" \
        --container_name "your_docker_container" \
        --workplace_name "workplace" \
        --max_iter_times 3
    cd ..
    ```

*   **For LLM-generated ideas:**
    ```bash
    cd research_agent
    python run_infer_idea.py \
        --category "your_category" \
        --model "gpt-4o-2024-08-06" \
        --container_name "your_docker_container" \
        --workplace_name "workplace" \
        --max_iter_times 3
    cd ..
    ```

**Key Parameters for `research_agent`:**

*   `--instance_path` (for `run_infer_plan.py`): Path to the benchmark JSON file defining the research task.
*   `--category` (for `run_infer_idea.py`): The research category to generate an idea for.
*   `--model`: Specifies the primary LLM to use (e.g., "gpt-4o-2024-08-06"). This corresponds to `COMPLETION_MODEL` in `research_agent/constant.py`.
*   `--container_name`: Name of the Docker container for code execution.
*   `--workplace_name`: Name of the subdirectory within the task-specific workspace where project files are stored (e.g., "workplace").
*   `--max_iter_times`: Number of iterations for the research agent to refine its approach.
*   **Note:** The `research_agent` also uses `CHEEP_MODEL` (defined in `research_agent/constant.py`) for less critical tasks.

**Output Directories from `research_agent`:**

Let `instance_id_from_benchmark` be the ID from the benchmark file (e.g., `your_instance_id`).
Let `instance_id_for_paths` be `instance_id_from_benchmark` if using `run_infer_plan.py`, or `instance_id_from_benchmark_idea` if `run_infer_idea.py` was used.
Let `sanitized_model_name` be the `--model` argument with `/` replaced by `__`.

*   **Cache Directory (Agent JSONs):**
    `./workplace_paper/cache_<instance_id_for_paths>_<sanitized_model_name>/agents/`
    *   Example: `./workplace_paper/cache_your_instance_id_idea_gpt-4o-2024-08-06/agents/`
*   **Workspace Directory (Code, Logs, etc.):**
    `./workplace_paper/task_<instance_id_for_paths>_<sanitized_model_name>/<workplace_name_arg>/`
    *   Example: `./workplace_paper/task_your_instance_id_idea_gpt-4o-2024-08-06/workplace/`
    *   This contains the `project/` directory with generated code.
*   **Log Directory:**
    `./log_<instance_id_for_paths>/` (Note: This is relative to where `research_agent` scripts are run, typically the project root, *not* inside `workplace_paper/`)
    *   Example: `./log_your_instance_id_idea/`

### Step 2: Run `paper_agent/writing.py`

After the `research_agent` has completed, run `writing.py` to generate the paper sections.

*   **Example Command (run from the project root directory):**
    ```bash
    python paper_agent/writing.py \
        --research_field "your_category" \
        --instance_id "your_instance_id" \
        --base_input_dir "." \
        --research_agent_workplace "workplace_paper" \
        --research_agent_model_name "gpt-4o-2024-08-06" \
        --research_agent_run_workplace "workplace" \
        --paper_output_dir "./paper_outputs/your_instance_id_paper"
        # Add --is_idea_run if run_infer_idea.py was used in Step 1
    ```
    If `run_infer_idea.py` was used, add the `--is_idea_run` flag:
    ```bash
    python paper_agent/writing.py \
        --research_field "your_category" \
        --instance_id "your_instance_id" \
        --base_input_dir "." \
        --research_agent_workplace "workplace_paper" \
        --research_agent_model_name "gpt-4o-2024-08-06" \
        --research_agent_run_workplace "workplace" \
        --is_idea_run \
        --paper_output_dir "./paper_outputs/your_instance_id_idea_paper"
    ```

**Key Parameters for `paper_agent/writing.py`:**

*   `--research_field`: Matches the `category` used in `research_agent` (e.g., "vq").
*   `--instance_id`: The original `instance_id` from the benchmark file (e.g., "rotation_vq"). This is crucial for locating the benchmark JSON.
*   `--base_input_dir`: Path to the project root directory where `workplace_paper/`, `benchmark/`, etc., are located. If running `writing.py` from the project root, this is `"."`. If running from `paper_agent/` directory, this would be `".."` .
*   `--research_agent_workplace`: The main output directory of the `research_agent` (e.g., "workplace_paper").
*   `--research_agent_model_name`: The `COMPLETION_MODEL` value used by `research_agent` (e.g., "gpt-4o-2024-08-06"). This must match the model used in Step 1 for paths to resolve correctly.
*   `--research_agent_run_workplace`: The value passed as `--workplace_name` to the `research_agent` script (e.g., "workplace").
*   `--is_idea_run`: A boolean flag. Include this if `research_agent/run_infer_idea.py` was used in Step 1. This affects how input paths are constructed.
*   `--paper_output_dir`: The directory where `paper_agent` will store all its outputs, including final LaTeX sections, intermediate logs, and checkpoints.

The `paper_agent` reads artifacts from the `research_agent`'s output directories (specifically `cache_.../agents/` and `task_.../project/model/` under `--base_input_dir`/`--research_agent_workplace`). It then writes its own outputs (LaTeX files, checkpoints, logs) into the specified `--paper_output_dir`.

## 3. Strategies for Improving Stability and Success Rate

Automated research and paper writing is complex. Here are some strategies to enhance stability and improve the quality of results:

*   **Input Quality:**
    *   The quality of source papers (for literature review components) or the initial idea (for `run_infer_plan.py`) significantly impacts the outcome. Ensure well-defined problems and relevant seed information.
    *   For dataset-driven tasks, ensure the dataset files in `benchmark/process/dataset_candidate/<category>/` are correctly formatted and the `metaprompt.py` effectively describes their usage.

*   **LLM Configuration:**
    *   The choice of `COMPLETION_MODEL` and `CHEEP_MODEL` (in `research_agent/constant.py`) is critical. More powerful models generally yield better results but are more expensive.
    *   Monitor API usage and costs. Ensure your API keys have sufficient quotas.

*   **Resource Management:**
    *   Ensure the Docker container specified by `--container_name` has adequate resources (CPU, memory).
    *   Be mindful of filesystem space, as logs and artifacts can consume considerable storage.

*   **Iterative Refinement:**
    *   **`research_agent`:** Use the `--max_iter_times` parameter to allow the agent more attempts to refine its code and experimental plan.
    *   **`paper_agent`:** If a specific paper section is unsatisfactory, you can delete its corresponding checkpoint directory within `<paper_output_dir>/<research_field>/<section_name>_checkpoints/` and re-run `paper_agent/writing.py`. The `paper_agent` will then regenerate that section from scratch while using checkpoints for other sections.

*   **Debugging - Key Log Files and Intermediate Outputs:**
    *   **`research_agent` Logs & Outputs:**
        *   **Main Logs:** Found in `./log_<instance_id_with_suffix>/` (e.g., `./log_rotation_vq/` or `./log_rotation_vq_idea/`). These contain detailed logs from the `InnoFlow` execution.
        *   **Agent JSONs:** Located in `./workplace_paper/cache_<instance_id_with_suffix>_<model_name>/agents/`. These files (`*.json`) are the structured outputs from each agent (e.g., `survey_agent.json`, `coding_plan_agent.json`, `machine_learning_agent.json`) and are crucial inputs for the `paper_agent`. Inspect these to understand the `research_agent`'s reasoning and outputs at each step.
        *   **Generated Code:** The actual code generated by `machine_learning_agent` can be found in `./workplace_paper/task_<instance_id_with_suffix>_<model_name>/<workplace_name_arg>/project/`.
    *   **`paper_agent` Logs & Outputs:**
        *   **Main Logs:** Each section composer logs to `<paper_output_dir>/<research_field>/<section_name>_composition.log` (e.g., `<paper_output_dir>/vq/methodology_composition.log`).
        *   **Intermediate GPT Calls:** Detailed intermediate logs and GPT call/response pairs for each section are stored in `<paper_output_dir>/<research_field>/temp/`. These are timestamped and can help diagnose issues during the content generation for a specific part of a section.
        *   **Checkpoints:** Found in `<paper_output_dir>/<research_field>/<section_name>_checkpoints/`. These store the generated structure and detailed content for subsections.

*   **Managing Expectations:**
    *   Autonomous research and paper generation is a cutting-edge field. While this pipeline automates many steps, results can vary. Expect to iterate, debug, and potentially manually refine the generated content for high-stakes publications.

## 4. Directory Structure Summary (Key Paths)

This outlines the typical directory structure when running the pipeline (assuming execution from the project root):

```
.
├── benchmark/
│   ├── final/<category>/<instance_id>.json  # Input for research_agent (run_infer_plan)
│   └── process/dataset_candidate/<category>/  # Datasets & metaprompt.py for research_agent
├── research_agent/
│   ├── run_infer_plan.py
│   └── run_infer_idea.py
├── paper_agent/
│   └── writing.py
├── workplace_paper/  # Default output/cache area for research_agent
│   ├── cache_<instance_id_with_suffix>_<model_name>/
│   │   └── agents/  # Agent JSONs (input for paper_agent)
│   └── task_<instance_id_with_suffix>_<model_name>/
│       └── <workplace_name_arg>/  # Workspace for a specific research_agent run
│           └── project/
│               └── model/  # Generated model code (input for paper_agent)
├── log_<instance_id_with_suffix>/  # Logs from research_agent (relative to execution dir)
└── <paper_output_dir>/  # User-defined output directory for paper_agent
    └── <research_field>/  # Matches --research_field argument
        ├── target_sections/  # Final generated LaTeX (.tex) files
        │   ├── abstract.tex
        │   ├── introduction.tex
        │   ├── related_work.tex
        │   ├── methodology.tex
        │   ├── experiments.tex
        │   └── conclusion.tex
        ├── <section_name>_checkpoints/  # Checkpoints for each section
        │   └── <instance_id_for_paths>/
        │       ├── structure.json
        │       └── subsections.json (or similar, depending on composer)
        ├── temp/  # Intermediate logs from paper_agent section composers
        └── <section_name>_composition.log  # Main log for each paper_agent section composer
```

**Notes on Paths:**

*   `<category>`: Your research field (e.g., "vq", "nlp").
*   `<instance_id>`: The base identifier for your task (e.g., "rotation_vq").
*   `<instance_id_with_suffix>`: This is `<instance_id>` if `run_infer_plan.py` was used, or `<instance_id>_idea` if `run_infer_idea.py` was used. For `paper_agent`, the internal variable `instance_id_for_paths` corresponds to this.
*   `<model_name>`: The sanitized LLM name (e.g., "gpt-4o-2024-08-06").
*   `<workplace_name_arg>`: The argument provided to `--workplace_name` for `research_agent`.
*   `<paper_output_dir>`: The argument provided to `--paper_output_dir` for `paper_agent`.

This guide should help you navigate the automated paper generation workflow. Remember that iterative refinement and careful examination of logs are key to achieving optimal results.
```
