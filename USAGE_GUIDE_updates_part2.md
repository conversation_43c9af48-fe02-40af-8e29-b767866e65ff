### Updates for `USAGE_GUIDE.md`

---

**1. Modify Section on "Input Data" (or equivalent, e.g., within "How to Run the Full Pipeline" -> "Step 1: Run `research_agent` script" -> "Key Parameters for `research_agent`", or as a new subsection in "Input Data")**

*Add the following explanation about the benchmark JSON structure:*

#### Benchmark JSON Structure (`--instance_path`)

The JSON file provided via `--instance_path` (for `run_infer_plan.py`) or used internally by `run_infer_idea.py` (derived from a category template) defines the research task. A key component of this JSON is the `source_papers` list.

Each item in the `source_papers` list describes a reference paper and can now optionally include a direct `github_link` to a code repository.

*   **`reference`**: (string) The title of the paper.
*   **`usage`**: (string) A brief note on how this paper is relevant or should be used.
*   **`github_link`**: (string, optional) A direct URL to a code repository for this paper. If provided, this link will be prioritized by the `PrepareAgent` during code search.
*   **`url`**: (string, optional) The arXiv URL for the paper. If provided (and `github_link` is not), the system will attempt to find official code links from the arXiv metadata using `find_official_code_for_paper`.

**Example `source_papers` entry in JSON:**
```json
{
    "instance_id": "example_instance",
    "url": "http://arxiv.org/abs/xxxx.xxxxx", // Main paper URL
    "source_papers": [
        {
            "reference": "Title of Paper 1",
            "usage": "Core concepts for the idea.",
            "url": "http://arxiv.org/abs/yyyy.yyyyy", // Specific URL for this source paper
            "github_link": "https://github.com/user/paper1_code" // Optional direct link
        },
        {
            "reference": "Title of Paper 2",
            "usage": "Secondary methods and comparisons.",
            "url": "http://arxiv.org/abs/zzzz.zzzzz" // Specific URL for this source paper
            // No github_link provided for Paper 2.
            // The system will use its 'url' to search arXiv metadata for code links.
            // If no 'url' here, it can't use metadata search for this specific paper.
        },
        {
            "reference": "Title of Paper 3 (No URL)",
            "usage": "General background."
            // No 'github_link' and no 'url'. Only general GitHub search by title will be used.
        }
    ],
    "task1": "Detailed description of the research task or innovative idea..."
    // ... rest of the JSON
}
```
Providing a `github_link` is the most direct way to guide the code search. If only a `url` (to arXiv) is provided, the system will attempt to find official links from the paper's metadata. If neither is provided, only general GitHub searches by title will be performed for that paper.

---

**2. Update Section "Strategies for Improving Stability and Success Rate" (or a relevant part of "How to Run")**

*Add the following sub-point, ideally under a subsection related to input quality or agent guidance:*

#### Optimizing Code Search with `source_papers`

The accuracy of the codebases selected by `PrepareAgent` significantly impacts downstream tasks. To improve the relevance of selected code:

*   **Utilize `github_link` in Benchmark JSON:** For key `source_papers` in your benchmark JSON file, directly provide a `github_link` to the most relevant official or high-quality community implementation. This is the most effective way to guide the agent.
*   **Code Search Prioritization:** The system now prioritizes code sources in the following order:
    1.  **User-Provided Links:** Direct `github_link`s you provide in the input JSON for `source_papers` (source type: `user_provided_official`).
    2.  **Tool-Discovered Official Links:** Code repositories automatically discovered from the metadata (e.g., arXiv comments, abstract) of the source papers, if their arXiv URLs are available (source type: `tool_discovered_official`).
    3.  **General GitHub Search Results:** Keyword-based searches on GitHub using paper titles (source type: `github_search`).
*   Providing direct links for crucial papers can significantly enhance the quality and relevance of the codebases chosen for the project, reducing the reliance on less precise search methods.

---

**3. Update Section "5. Interactive Mode and Human-in-the-Loop Features"**

*Add the following new sub-subsection:*

#### 3. Human Code Review (in `run_infer_plan.py` and `run_infer_idea.py` during `MLAgent` execution)

This feature allows you to directly review and modify the code generated by the `MLAgent` at critical stages. The project code resides in the `/{workplace_name}/project/` directory within the Docker container.

*   **Timing:** Code review points occur:
    1.  **Initial Code Generation:** After the `MLAgent` first generates the complete project code based on the coding plan, but *before* this code is evaluated by the `JudgeAgent`.
    2.  **Iterative Refinement Loop:** During the refinement loop, after the `JudgeAgent` has provided its feedback on the code from the previous iteration, and *before* the `MLAgent` attempts to refine the code further based on this feedback.

*   **Process:**
    *   When a code review point is reached, the script will invoke `manage_code_review_session`.
    *   This utility automatically downloads the current state of the generated project code (from `/{workplace_name}/project/` within Docker) to a temporary directory on your local machine.
    *   The path to this local directory (e.g., `./temp_human_code_review_sessions/review_timestamp_xxxx/project/`) will be printed in the console.
    *   You should then open this local directory (specifically the `project` subfolder within it) in your preferred code editor.
    *   Review the code, make any necessary corrections, additions, or improvements, and save your changes directly to these local files.
    *   Once you have completed all your edits and saved the files, return to the console where the script is paused and press **Enter** to continue.
    *   Your modified code from the local session directory will be uploaded back into the Docker container, overwriting the previous state of the `/{workplace_name}/project/` directory.
    *   The temporary review directory on your host machine will then be automatically deleted to save space.

*   **Impact on Subsequent Steps:**
    *   **Initial Review:** If you edit the code during the first review (before any `JudgeAgent` evaluation), the `JudgeAgent` will subsequently evaluate your human-modified version of the code.
    *   **Iterative Review:** If you edit the code within the refinement loop, the `MLAgent` will be explicitly informed in its next prompt that human modifications have occurred. It will be instructed to consider your changes alongside the `JudgeAgent`'s most recent feedback. Your direct edits should generally be prioritized by the LLM, especially if they conflict with automated suggestions for the same code sections, but the LLM will still attempt to address other valid points from the `JudgeAgent`'s feedback if applicable and compatible with your changes.

*   **Error Handling:**
    *   The `manage_code_review_session` utility is designed to handle basic errors. However, if a significant error occurs during the code download or upload process, or if you manually interrupt the script (e.g., Ctrl+C) while it's paused for your review, the review session may not complete successfully.
    *   The script is designed to halt (via `SystemExit`) if `manage_code_review_session` reports a failure, preventing execution with potentially inconsistent code states.

---
